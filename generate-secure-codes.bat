@echo off
chcp 65001 >nul 2>&1
title Ultra-Secure Activation Code Generator v3.0

:MAIN_MENU
cls
echo.
echo ================================================================
echo          Ultra-Secure Code Generator v3.0
echo                TRUE One-Time Use System
echo ================================================================
echo.
echo SECURITY FEATURES:
echo ----------------------------------------------------------------
echo.
echo BASIC Security:     Machine ID + Encryption + One-Time Use
echo ENHANCED Security:  + Hardware Fingerprint + IP Validation
echo MAXIMUM Security:   + Behavioral Analysis + Geo-Restrictions
echo.
echo ----------------------------------------------------------------
echo.
echo GENERATION OPTIONS:
echo.
echo [1] Generate Basic Security Code
echo [2] Generate Enhanced Security Code  
echo [3] Generate Maximum Security Code
echo [4] Generate Trial Code (Time-Limited)
echo [5] View System Statistics
echo [6] Validate Activation Code
echo [7] System Maintenance
echo [8] Help and Documentation
echo [9] Exit
echo.
set /p choice="Select option (1-9): "

if "%choice%"=="1" goto BASIC_CODE
if "%choice%"=="2" goto ENHANCED_CODE
if "%choice%"=="3" goto MAXIMUM_CODE
if "%choice%"=="4" goto TRIAL_CODE
if "%choice%"=="5" goto VIEW_STATS
if "%choice%"=="6" goto VALIDATE_CODE
if "%choice%"=="7" goto MAINTENANCE
if "%choice%"=="8" goto HELP
if "%choice%"=="9" goto EXIT

echo Invalid choice. Please try again.
pause
goto MAIN_MENU

:BASIC_CODE
cls
echo.
echo ================================================================
echo                Basic Security Code Generator
echo ================================================================
echo.
echo Security Features:
echo - Machine ID binding
echo - AES-256 encryption
echo - TRUE one-time use (code destroyed after use)
echo - Format verification
echo.
set /p client_name="Enter client name: "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="Enter machine ID (optional): "

echo.
echo Generating basic security activation code...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const result = generator.generateUltraSecureCode({
  clientName: '%client_name%',
  machineId: '%machine_id%' || null,
  securityLevel: 'BASIC',
  type: 'LIFETIME'
});

if (result.success) {
  console.log('SUCCESS: Basic Security Code Generated!');
  console.log('='.repeat(50));
  console.log('Activation Code: ' + result.activationCode);
  console.log('Client: ' + result.clientName);
  console.log('Security Level: ' + result.securityLevelName);
  console.log('Generated: ' + result.generatedAt);
  console.log('');
  console.log('IMPORTANT: This code can only be used ONCE!');
  console.log('After validation, it will be permanently destroyed.');
} else {
  console.log('ERROR: Failed to generate code');
  console.log('Reason: ' + result.error);
}
"

echo.
pause
goto MAIN_MENU

:ENHANCED_CODE
cls
echo.
echo ================================================================
echo              Enhanced Security Code Generator
echo ================================================================
echo.
echo Security Features:
echo - Hardware fingerprinting
echo - IP address validation
echo - TRUE one-time use
echo - Time-based validation windows
echo - Multi-device management
echo.
set /p client_name="Enter client name: "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="Enter machine ID (recommended): "

set /p allowed_ips="Enter allowed IP addresses (comma-separated, optional): "

set /p max_devices="Maximum allowed devices (default: 1): "
if "%max_devices%"=="" set max_devices=1

echo.
echo Generating enhanced security activation code...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const allowedIPs = '%allowed_ips%' ? '%allowed_ips%'.split(',').map(ip => ip.trim()) : [];

const result = generator.generateUltraSecureCode({
  clientName: '%client_name%',
  machineId: '%machine_id%' || null,
  securityLevel: 'ENHANCED',
  type: 'LIFETIME',
  allowedIPs: allowedIPs,
  maxDevices: parseInt('%max_devices%') || 1
});

if (result.success) {
  console.log('SUCCESS: Enhanced Security Code Generated!');
  console.log('='.repeat(50));
  console.log('Activation Code: ' + result.activationCode);
  console.log('Client: ' + result.clientName);
  console.log('Security Level: ' + result.securityLevelName);
  console.log('Generated: ' + result.generatedAt);
  console.log('Max Devices: ' + result.metadata.maxDevices);
  console.log('IP Restriction: ' + (result.metadata.ipRestriction ? 'YES' : 'NO'));
  console.log('');
  console.log('CRITICAL: This code can only be used ONCE!');
  console.log('Hardware fingerprint will be captured on first use.');
} else {
  console.log('ERROR: Failed to generate code');
  console.log('Reason: ' + result.error);
}
"

echo.
pause
goto MAIN_MENU

:MAXIMUM_CODE
cls
echo.
echo ================================================================
echo              Maximum Security Code Generator
echo ================================================================
echo.
echo Security Features:
echo - ALL Enhanced features
echo - Behavioral analysis
echo - Geographic restrictions
echo - Anti-tampering detection
echo - Continuous monitoring
echo.
set /p client_name="Enter client name: "
if "%client_name%"=="" set client_name=Licensed User

set /p machine_id="Enter machine ID (REQUIRED): "
if "%machine_id%"=="" (
    echo ERROR: Machine ID is required for maximum security!
    pause
    goto MAIN_MENU
)

set /p allowed_ips="Enter allowed IP addresses (comma-separated): "

set /p geo_restriction="Enter country code restriction (optional): "

echo.
echo Generating MAXIMUM security activation code...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const allowedIPs = '%allowed_ips%' ? '%allowed_ips%'.split(',').map(ip => ip.trim()) : [];

const result = generator.generateUltraSecureCode({
  clientName: '%client_name%',
  machineId: '%machine_id%',
  securityLevel: 'MAXIMUM',
  type: 'LIFETIME',
  allowedIPs: allowedIPs,
  maxDevices: 1,
  geoRestriction: '%geo_restriction%' || null
});

if (result.success) {
  console.log('SUCCESS: MAXIMUM Security Code Generated!');
  console.log('='.repeat(50));
  console.log('Activation Code: ' + result.activationCode);
  console.log('Client: ' + result.clientName);
  console.log('Security Level: ' + result.securityLevelName);
  console.log('Generated: ' + result.generatedAt);
  console.log('Machine Binding: YES');
  console.log('IP Restriction: ' + (result.metadata.ipRestriction ? 'YES' : 'NO'));
  console.log('Geo Restriction: ' + (result.metadata.geoRestriction ? 'YES' : 'NO'));
  console.log('');
  console.log('MAXIMUM SECURITY: This code has ALL protection layers!');
  console.log('Can only be used ONCE - then permanently destroyed.');
} else {
  console.log('ERROR: Failed to generate code');
  console.log('Reason: ' + result.error);
}
"

echo.
pause
goto MAIN_MENU

:TRIAL_CODE
cls
echo.
echo ================================================================
echo                Trial Code Generator
echo ================================================================
echo.
echo Trial Options:
echo [1] 1 Day Trial
echo [2] 7 Days Trial  
echo [3] 30 Days Trial
echo.
set /p trial_choice="Select trial duration (1-3): "

if "%trial_choice%"=="1" set trial_days=1
if "%trial_choice%"=="2" set trial_days=7
if "%trial_choice%"=="3" set trial_days=30
if "%trial_days%"=="" set trial_days=7

echo.
echo Security Level:
echo [1] BASIC
echo [2] ENHANCED
echo [3] MAXIMUM
echo.
set /p security_choice="Select security level (1-3): "

if "%security_choice%"=="1" set security_level=BASIC
if "%security_choice%"=="2" set security_level=ENHANCED
if "%security_choice%"=="3" set security_level=MAXIMUM
if "%security_level%"=="" set security_level=ENHANCED

set /p client_name="Enter client name: "
if "%client_name%"=="" set client_name=Trial User

echo.
echo Generating %trial_days%-day trial code with %security_level% security...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const result = generator.generateUltraSecureCode({
  clientName: '%client_name%',
  securityLevel: '%security_level%',
  type: 'TRIAL',
  trialDays: %trial_days%
});

if (result.success) {
  console.log('SUCCESS: Trial Code Generated!');
  console.log('='.repeat(40));
  console.log('Activation Code: ' + result.activationCode);
  console.log('Client: ' + result.clientName);
  console.log('Trial Duration: %trial_days% days');
  console.log('Security Level: ' + result.securityLevelName);
  console.log('Expires: ' + result.expiryDate);
  console.log('');
  console.log('TRIAL LIMITATIONS:');
  console.log('- Valid for %trial_days% days only');
  console.log('- One-time use activation');
  console.log('- Cannot be extended');
} else {
  console.log('ERROR: Failed to generate trial code');
  console.log('Reason: ' + result.error);
}
"

echo.
pause
goto MAIN_MENU

:VIEW_STATS
cls
echo.
echo ================================================================
echo                System Statistics
echo ================================================================
echo.
echo Loading system statistics...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const stats = generator.getSystemStatistics();

if (stats.error) {
  console.log('ERROR: ' + stats.error);
} else {
  console.log('SYSTEM STATISTICS');
  console.log('='.repeat(40));
  console.log('Total Codes Generated: ' + stats.totalCodes);
  console.log('Codes Used: ' + stats.usedCodes);
  console.log('Codes Available: ' + stats.availableCodes);
  console.log('Trial Codes: ' + stats.trialCodes);
  console.log('Lifetime Codes: ' + stats.lifetimeCodes);
  console.log('Total Clients: ' + stats.totalClients);
  console.log('');
  console.log('SECURITY BREAKDOWN:');
  console.log('Basic Security: ' + stats.securityBreakdown.BASIC);
  console.log('Enhanced Security: ' + stats.securityBreakdown.ENHANCED);
  console.log('Maximum Security: ' + stats.securityBreakdown.MAXIMUM);
  console.log('');
  console.log('System Version: ' + stats.systemVersion);
  console.log('Database Created: ' + stats.databaseCreated);
  console.log('Last Activity: ' + stats.lastActivity);
}
"

echo.
pause
goto MAIN_MENU

:VALIDATE_CODE
cls
echo.
echo ================================================================
echo                Code Validation Tool
echo ================================================================
echo.
set /p code_to_validate="Enter activation code to validate: "
set /p machine_id_validate="Enter machine ID (optional): "
set /p ip_address="Enter IP address (optional): "

echo.
echo Validating activation code...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const validationData = {
  machineId: '%machine_id_validate%' || null,
  ipAddress: '%ip_address%' || '127.0.0.1',
  timestamp: Date.now()
};

const result = generator.validateActivationCode('%code_to_validate%', validationData);

if (result.valid) {
  console.log('SUCCESS: ACTIVATION CODE IS VALID');
  console.log('='.repeat(40));
  console.log('Client: ' + result.data.clientName);
  console.log('Type: ' + result.data.type);
  console.log('Security Level: ' + result.data.securityLevel);
  console.log('Generated: ' + result.data.generatedAt);
  console.log('Validated: ' + result.data.validatedAt);
  console.log('');
  console.log('IMPORTANT: ' + result.message);
  console.log('This code has been CONSUMED and cannot be used again!');
} else {
  console.log('FAILED: ACTIVATION CODE IS INVALID');
  console.log('='.repeat(40));
  console.log('Error: ' + result.error);
  console.log('Error Code: ' + result.errorCode);
}
"

echo.
pause
goto MAIN_MENU

:MAINTENANCE
cls
echo.
echo ================================================================
echo                System Maintenance
echo ================================================================
echo.
echo Maintenance Options:
echo.
echo [1] Clean Expired Trial Codes
echo [2] View Audit Log (Last 10 entries)
echo [3] Back to Main Menu
echo.
set /p maint_choice="Select option (1-3): "

if "%maint_choice%"=="1" goto CLEAN_EXPIRED
if "%maint_choice%"=="2" goto VIEW_AUDIT
if "%maint_choice%"=="3" goto MAIN_MENU

echo Invalid choice.
pause
goto MAINTENANCE

:CLEAN_EXPIRED
echo.
echo Cleaning expired trial codes...
echo.

node -e "
const Generator = require('./ultra-secure-code-generator');
const generator = new Generator();

const result = generator.cleanExpiredCodes();

if (result.success) {
  console.log('SUCCESS: Maintenance completed');
  console.log('Expired codes cleaned: ' + result.cleanedCount);
} else {
  console.log('ERROR: Maintenance failed');
  console.log('Reason: ' + result.error);
}
"

echo.
pause
goto MAINTENANCE

:VIEW_AUDIT
echo.
echo Recent Security Events (Last 10):
echo.

node -e "
const fs = require('fs');
const path = require('path');

try {
  const auditFile = path.join(__dirname, 'security-audit.log');
  if (fs.existsSync(auditFile)) {
    const content = fs.readFileSync(auditFile, 'utf8');
    const lines = content.trim().split('\\n');
    const lastTen = lines.slice(-10);

    lastTen.forEach((line, index) => {
      try {
        const event = JSON.parse(line);
        console.log((index + 1) + '. [' + event.timestamp + '] ' + event.eventType + ': ' + event.message);
      } catch (e) {
        console.log((index + 1) + '. ' + line);
      }
    });
  } else {
    console.log('No audit log found.');
  }
} catch (error) {
  console.log('Error reading audit log: ' + error.message);
}
"

echo.
pause
goto MAINTENANCE

:HELP
cls
echo.
echo ================================================================
echo              Help and Documentation
echo ================================================================
echo.
echo ULTRA-SECURE CODE GENERATOR v3.0
echo.
echo SECURITY LEVELS:
echo.
echo 1. BASIC SECURITY:
echo    - Machine ID binding
echo    - AES-256 encryption
echo    - TRUE one-time use
echo.
echo 2. ENHANCED SECURITY:
echo    - All Basic features
echo    - Hardware fingerprinting
echo    - IP address validation
echo    - Time-based validation windows
echo.
echo 3. MAXIMUM SECURITY:
echo    - All Enhanced features
echo    - Behavioral analysis
echo    - Geographic restrictions
echo    - Anti-tampering detection
echo    - Continuous monitoring
echo.
echo KEY FEATURES:
echo - TRUE One-Time Use: Codes are destroyed after validation
echo - No code reuse possible
echo - Comprehensive audit logging
echo - Multiple security layers
echo - Enterprise-grade protection
echo.
echo SUPPORT:
echo Phone: +213 551 93 05 89
echo Email: <EMAIL>
echo Website: www.icodedz.com
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ================================================================
echo          Thank you for using Ultra-Secure Generator v3.0!
echo ================================================================
echo.
echo Your activation codes are protected with:
echo - TRUE one-time use technology
echo - Enterprise-grade security
echo - Comprehensive audit trails
echo - Multiple protection layers
echo.
echo For support: +213 551 93 05 89
echo.
pause
exit

:ERROR
echo.
echo ERROR: An error occurred. Please check:
echo - Node.js is installed and in PATH
echo - All required files are present
echo - You have write permissions
echo.
pause
goto MAIN_MENU
